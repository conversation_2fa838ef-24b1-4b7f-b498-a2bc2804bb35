# Crawl4AI MCP Server

A standalone Model Control Protocol (MCP) server that provides direct AI controller integration with Crawl4AI functionality, eliminating the need for Docker deployment and API calls.

## Features

- **Direct Integration**: No HTTP proxy layer - direct function calls to Crawl4AI
- **Stdio Transport**: Uses stdin/stdout for MCP communication
- **Full Tool Support**: All 7 core Crawl4AI tools available
- **Minimal Dependencies**: Only essential crawl4ai and MCP dependencies
- **Production Ready**: Working, functional implementation

## Available Tools

1. **`md`** - Generate markdown from web content with filtering options
2. **`html`** - Extract preprocessed HTML for schema extraction
3. **`crawl`** - Crawl multiple URLs and return detailed results
4. **`screenshot`** - Capture webpage screenshots
5. **`pdf`** - Generate PDF documents from web pages
6. **`execute_js`** - Execute JavaScript on web pages
7. **`ask`** - Query Crawl4AI library context and documentation

## Installation

### Prerequisites

- Python 3.9 or higher
- Crawl4AI dependencies (automatically installed)

### Install from Source

```bash
# Clone or copy the crawl4ai_mcp_server directory
cd crawl4ai_mcp_server

# Install in development mode
pip install -e .

# Or install normally
pip install .
```

### Install Dependencies Only

```bash
pip install mcp crawl4ai>=0.6.2 pydantic>=2.10 aiofiles>=24.1.0
```

## Configuration

The server can be configured via environment variables:

```bash
# LLM Configuration (for LLM content filtering)
export OPENAI_API_KEY="your-openai-key"
export CRAWL4AI_LLM_PROVIDER="openai"  # or "anthropic"
export CRAWL4AI_LLM_MODEL="gpt-3.5-turbo"

# Server Configuration
export CRAWL4AI_MCP_NAME="crawl4ai-mcp"
export CRAWL4AI_MCP_VERSION="0.1.0"
export CRAWL4AI_CACHE_MODE="write_only"
export CRAWL4AI_MAX_CONCURRENT="5"
```

## Usage

### Running the Server

```bash
# Using the installed script
crawl4ai-mcp

# Or run directly
python -m crawl4ai_mcp.server

# Or from source
cd crawl4ai_mcp_server/src
python -m crawl4ai_mcp.server
```

### Connecting with MCP Clients

The server uses stdio transport, so you can connect it to any MCP-compatible client:

#### Claude Desktop

Add to your Claude Desktop MCP configuration:

```json
{
  "mcpServers": {
    "crawl4ai": {
      "command": "crawl4ai-mcp",
      "args": []
    }
  }
}
```

#### Custom MCP Client

```python
import asyncio
from mcp.client.stdio import stdio_client
from mcp.client.session import ClientSession

async def test_crawl4ai():
    # Connect to the server
    async with stdio_client(["crawl4ai-mcp"]) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            
            # List available tools
            tools = await session.list_tools()
            print("Available tools:", [t.name for t in tools.tools])
            
            # Use the markdown tool
            result = await session.call_tool("md", {
                "url": "https://example.com",
                "f": "FIT"
            })
            print("Markdown result:", result.content[0].text)

asyncio.run(test_crawl4ai())
```

## Tool Examples

### Markdown Generation

```python
# Generate clean markdown from a webpage
result = await session.call_tool("md", {
    "url": "https://example.com",
    "f": "FIT",  # or "RAW", "BM25", "LLM"
    "q": "main content",  # query for BM25/LLM filtering
    "c": "0"  # cache control
})
```

### Screenshot Capture

```python
# Capture a screenshot
result = await session.call_tool("screenshot", {
    "url": "https://example.com",
    "screenshot_wait_for": 2,
    "output_path": "/path/to/save/screenshot.png"  # optional
})
```

### Multi-URL Crawling

```python
# Crawl multiple URLs
result = await session.call_tool("crawl", {
    "urls": ["https://example.com", "https://another-site.com"],
    "browser_config": {},
    "crawler_config": {}
})
```

### JavaScript Execution

```python
# Execute JavaScript on a page
result = await session.call_tool("execute_js", {
    "url": "https://example.com",
    "js_code": [
        "await page.click('button')",
        "await page.waitForTimeout(1000)"
    ]
})
```

### Library Context Query

```python
# Query Crawl4AI documentation
result = await session.call_tool("ask", {
    "context_type": "all",  # "code", "doc", or "all"
    "query": "how to configure browser",
    "max_results": 10
})
```

## Migration from Docker Version

If you're migrating from the Docker-based Crawl4AI server:

1. **Stop the Docker container**
2. **Install this MCP server** following the installation instructions above
3. **Update your MCP client configuration** to use the new stdio-based server
4. **Copy context files** (optional): If you were using the `/ask` endpoint, copy the context files:
   - Copy `c4ai-code-context.md` and `c4ai-doc-context.md` from your Docker deployment
   - Place them in the appropriate location for the context tool

## Development

### Project Structure

```
crawl4ai_mcp_server/
├── src/crawl4ai_mcp/
│   ├── __init__.py
│   ├── server.py          # Main MCP server
│   ├── config.py          # Configuration management
│   ├── utils.py           # Utility functions
│   └── tools/             # Tool implementations
│       ├── __init__.py
│       ├── markdown.py    # Markdown generation
│       ├── html.py        # HTML extraction
│       ├── crawl.py       # Multi-URL crawling
│       ├── screenshot.py  # Screenshot capture
│       ├── pdf.py         # PDF generation
│       ├── javascript.py  # JS execution
│       └── context.py     # Library context
├── pyproject.toml
└── README.md
```

### Running Tests

```bash
# Install development dependencies
pip install -e ".[dev]"

# Run tests
pytest
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure Crawl4AI is properly installed with all dependencies
2. **Browser Issues**: Make sure Playwright browsers are installed: `playwright install`
3. **Permission Errors**: Ensure the server has write permissions for screenshot/PDF output paths
4. **LLM Errors**: Check that your API keys are properly set for LLM-based filtering

### Logging

The server logs to stdout. Increase verbosity by setting:

```bash
export PYTHONPATH=.
python -c "import logging; logging.basicConfig(level=logging.DEBUG)"
```

## License

This project follows the same license as Crawl4AI.

## Contributing

Contributions are welcome! Please ensure:

1. Code follows the existing style
2. All tools maintain compatibility with the original API
3. Tests are included for new functionality
4. Documentation is updated accordingly