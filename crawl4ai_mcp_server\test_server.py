#!/usr/bin/env python3
"""
Test script for Crawl4AI MCP Server.

This script tests the standalone MCP server functionality by connecting
to it via stdio and executing various tools.
"""

import asyncio
import json
import subprocess
import sys
from pathlib import Path

# Add the src directory to Python path for testing
sys.path.insert(0, str(Path(__file__).parent / "src"))

from mcp.client.stdio import stdio_client
from mcp.client.session import ClientSession


async def test_server_connection():
    """Test basic server connection and tool listing."""
    print("🔌 Testing server connection...")
    
    try:
        # Start the server process
        server_cmd = [sys.executable, "-m", "crawl4ai_mcp.server"]
        
        async with stdio_client(server_cmd) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                print("✅ Server connection successful!")
                
                # List available tools
                tools_response = await session.list_tools()
                tools = [tool.name for tool in tools_response.tools]
                print(f"📋 Available tools: {tools}")
                
                expected_tools = ["md", "html", "crawl", "screenshot", "pdf", "execute_js", "ask"]
                missing_tools = set(expected_tools) - set(tools)
                if missing_tools:
                    print(f"⚠️  Missing tools: {missing_tools}")
                else:
                    print("✅ All expected tools are available!")
                
                return session, tools
                
    except Exception as e:
        print(f"❌ Server connection failed: {e}")
        return None, []


async def test_markdown_tool(session: ClientSession):
    """Test the markdown generation tool."""
    print("\n📝 Testing markdown tool...")
    
    try:
        result = await session.call_tool("md", {
            "url": "https://example.com",
            "f": "fit",
            "c": "0"
        })
        
        response_data = json.loads(result.content[0].text)
        
        if response_data.get("success"):
            print("✅ Markdown tool test successful!")
            print(f"   URL: {response_data.get('url')}")
            print(f"   Filter: {response_data.get('filter')}")
            print(f"   Markdown length: {len(response_data.get('markdown', ''))}")
        else:
            print(f"❌ Markdown tool failed: {response_data.get('detail', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Markdown tool test failed: {e}")


async def test_html_tool(session: ClientSession):
    """Test the HTML extraction tool."""
    print("\n🌐 Testing HTML tool...")
    
    try:
        result = await session.call_tool("html", {
            "url": "https://example.com"
        })
        
        response_data = json.loads(result.content[0].text)
        
        if response_data.get("success"):
            print("✅ HTML tool test successful!")
            print(f"   URL: {response_data.get('url')}")
            print(f"   HTML length: {len(response_data.get('html', ''))}")
        else:
            print(f"❌ HTML tool failed: {response_data.get('detail', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ HTML tool test failed: {e}")


async def test_crawl_tool(session: ClientSession):
    """Test the multi-URL crawling tool."""
    print("\n🕷️  Testing crawl tool...")
    
    try:
        result = await session.call_tool("crawl", {
            "urls": ["https://example.com"],
            "browser_config": {},
            "crawler_config": {}
        })
        
        response_data = json.loads(result.content[0].text)
        
        if isinstance(response_data, list) and len(response_data) > 0:
            crawl_result = response_data[0]
            if crawl_result.get("success"):
                print("✅ Crawl tool test successful!")
                print(f"   URL: {crawl_result.get('url')}")
                print(f"   Success: {crawl_result.get('success')}")
            else:
                print(f"❌ Crawl failed: {crawl_result.get('error_message', 'Unknown error')}")
        else:
            print(f"❌ Crawl tool returned unexpected format: {type(response_data)}")
            
    except Exception as e:
        print(f"❌ Crawl tool test failed: {e}")


async def test_context_tool(session: ClientSession):
    """Test the library context tool."""
    print("\n📚 Testing context tool...")
    
    try:
        result = await session.call_tool("ask", {
            "context_type": "all",
            "query": "crawl",
            "max_results": 5
        })
        
        response_data = json.loads(result.content[0].text)
        
        if isinstance(response_data, dict):
            print("✅ Context tool test successful!")
            if "code_results" in response_data:
                print(f"   Code results: {len(response_data['code_results'])}")
            if "doc_results" in response_data:
                print(f"   Doc results: {len(response_data['doc_results'])}")
        else:
            print(f"❌ Context tool returned unexpected format: {type(response_data)}")
            
    except Exception as e:
        print(f"❌ Context tool test failed: {e}")


async def test_error_handling(session: ClientSession):
    """Test error handling with invalid inputs."""
    print("\n🚨 Testing error handling...")
    
    try:
        # Test with missing required parameter
        result = await session.call_tool("md", {})
        response_data = json.loads(result.content[0].text)
        
        if "error" in response_data:
            print("✅ Error handling test successful!")
            print(f"   Error code: {response_data.get('error')}")
            print(f"   Error detail: {response_data.get('detail')}")
        else:
            print("❌ Expected error response but got success")
            
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")


async def main():
    """Main test function."""
    print("🧪 Starting Crawl4AI MCP Server Tests")
    print("=" * 50)
    
    # Test server connection
    session, tools = await test_server_connection()
    
    if not session:
        print("❌ Cannot proceed with tests - server connection failed")
        return
    
    # Run tool tests
    if "md" in tools:
        await test_markdown_tool(session)
    
    if "html" in tools:
        await test_html_tool(session)
    
    if "crawl" in tools:
        await test_crawl_tool(session)
    
    if "ask" in tools:
        await test_context_tool(session)
    
    # Test error handling
    await test_error_handling(session)
    
    print("\n" + "=" * 50)
    print("🏁 Test suite completed!")


if __name__ == "__main__":
    asyncio.run(main())