"""Multi-URL crawling tool for Crawl4AI MCP Server."""

from typing import Dict, Any, List, Optional

import mcp.types as types
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig

from ..utils import create_json_response, create_error_response, create_tool_schema


class CrawlTool:
    """Tool for crawling multiple URLs and returning detailed results."""
    
    @staticmethod
    def get_schema() -> types.Tool:
        """Get the tool schema for MCP."""
        return types.Tool(
            name="crawl",
            description="Crawl a list of URLs and return the results as JSON with full crawl details",
            inputSchema=create_tool_schema(
                properties={
                    "urls": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of URLs to crawl"
                    },
                    "browser_config": {
                        "type": "object",
                        "description": "Browser configuration options",
                        "default": {}
                    },
                    "crawler_config": {
                        "type": "object", 
                        "description": "Crawler configuration options",
                        "default": {}
                    }
                },
                required=["urls"]
            )
        )
    
    @staticmethod
    async def execute(arguments: Dict[str, Any]) -> List[types.TextContent]:
        """Execute the crawl tool."""
        try:
            # Extract and validate arguments
            urls = arguments.get("urls", [])
            if not urls:
                return create_error_response("At least one URL is required", 400)
            
            browser_config_dict = arguments.get("browser_config", {})
            crawler_config_dict = arguments.get("crawler_config", {})
            
            # Perform crawl
            results = await CrawlTool._crawl_urls(
                urls, browser_config_dict, crawler_config_dict
            )
            
            return create_json_response(results)
            
        except Exception as e:
            return create_error_response(f"Crawl failed: {str(e)}")
    
    @staticmethod
    async def _crawl_urls(
        urls: List[str],
        browser_config: Dict[str, Any],
        crawler_config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Crawl multiple URLs and return results."""
        
        # Create browser and crawler configs
        browser_cfg = BrowserConfig(**browser_config) if browser_config else BrowserConfig()
        crawler_cfg = CrawlerRunConfig(**crawler_config) if crawler_config else CrawlerRunConfig()
        
        results = []
        
        async with AsyncWebCrawler(config=browser_cfg) as crawler:
            for url in urls:
                try:
                    # Validate URL
                    if not url.startswith(('http://', 'https://')):
                        url = 'https://' + url
                    
                    # Crawl the URL
                    crawl_results = await crawler.arun(url=url, config=crawler_cfg)
                    
                    if crawl_results and len(crawl_results) > 0:
                        # Convert CrawlResult to dict for JSON serialization
                        result_dict = crawl_results[0].model_dump()
                        results.append(result_dict)
                    else:
                        results.append({
                            "url": url,
                            "success": False,
                            "error_message": "No results returned",
                            "html": "",
                            "markdown": None
                        })
                        
                except Exception as e:
                    results.append({
                        "url": url,
                        "success": False,
                        "error_message": str(e),
                        "html": "",
                        "markdown": None
                    })
        
        return results