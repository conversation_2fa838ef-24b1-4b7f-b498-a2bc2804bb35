#!/usr/bin/env python3
"""
Simple syntax validation script for Crawl4AI MCP Server.
"""

import ast
import sys
from pathlib import Path


def validate_python_file(file_path):
    """Validate Python syntax in a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Try to parse the AST
        ast.parse(content)
        print(f"✅ {file_path.name}: Syntax OK")
        return True
        
    except SyntaxError as e:
        print(f"❌ {file_path.name}: Syntax Error at line {e.lineno}: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ {file_path.name}: Error: {e}")
        return False


def main():
    """Main validation function."""
    print("🔍 Validating Python syntax...")
    
    # Find all Python files
    src_dir = Path(__file__).parent / "src"
    python_files = list(src_dir.rglob("*.py"))
    
    if not python_files:
        print("❌ No Python files found!")
        return False
    
    all_valid = True
    for py_file in python_files:
        if not validate_python_file(py_file):
            all_valid = False
    
    if all_valid:
        print(f"\n✅ All {len(python_files)} Python files have valid syntax!")
    else:
        print(f"\n❌ Some files have syntax errors!")
    
    return all_valid


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)