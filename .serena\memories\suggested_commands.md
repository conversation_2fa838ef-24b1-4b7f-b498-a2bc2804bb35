# Windows环境下的建议命令

## 项目安装和设置

### 安装项目
```cmd
# 安装Crawl4AI包
pip install -U crawl4ai

# 安装预发布版本
pip install crawl4ai --pre

# 运行安装后设置
crawl4ai-setup

# 验证安装
crawl4ai-doctor

# 手动安装浏览器(如遇到浏览器问题)
python -m playwright install --with-deps chromium
```

### 开发环境安装
```cmd
# 克隆或进入项目目录
cd C:\dev\MCP\crawl4ai-origin

# 安装开发依赖
pip install -e .

# 安装可选依赖
pip install "crawl4ai[all]"  # 所有依赖
pip install "crawl4ai[torch]"  # 机器学习依赖
pip install "crawl4ai[pdf]"   # PDF处理
```

## 主要命令行工具

### crwl命令(新CLI界面)
```cmd
# 基本爬取并输出markdown
crwl https://www.nbcnews.com/business -o markdown

# 深度爬取(BFS策略，最多10页)
crwl https://docs.crawl4ai.com --deep-crawl bfs --max-pages 10

# 使用LLM提取特定问题
crwl https://www.example.com/products -q "Extract all product prices"
```

## 测试命令

### 运行测试
```cmd
# 运行所有测试
python -m pytest tests/

# 运行特定测试目录
python -m pytest tests/async/
python -m pytest tests/browser/
python -m pytest tests/cli/

# 运行特定测试文件
python -m pytest tests/test_main.py
python -m pytest tests/test_web_crawler.py

# 详细输出
python -m pytest tests/ -v

# 显示测试覆盖率
python -m pytest tests/ --cov=crawl4ai
```

## 代码质量和格式化

### 代码格式化
```cmd
# 使用black格式化代码
python -m black crawl4ai/
python -m black tests/

# 检查格式化
python -m black --check crawl4ai/
```

### 代码检查
```cmd
# 使用flake8检查代码
python -m flake8 crawl4ai/

# 安全检查(bandit)
python -m bandit -r crawl4ai/
```

## Docker相关命令

### Docker构建和运行
```cmd
# 构建Docker镜像
docker build -t crawl4ai .

# 运行Docker容器
docker-compose up

# 停止容器
docker-compose down
```

## Windows实用命令

### 文件系统操作
```cmd
# 列出目录内容
dir
dir /s  # 递归列出

# 查找文件
where python
where pip

# 搜索文件内容
findstr "pattern" *.py
findstr /s /i "AsyncWebCrawler" *.py  # 递归不区分大小写搜索

# 创建目录
mkdir dirname

# 删除文件/目录
del filename
rmdir /s dirname  # 递归删除目录
```

### Git操作
```cmd
# 基本Git命令
git status
git add .
git commit -m "commit message"
git push
git pull

# 分支操作
git branch
git checkout -b new-branch
git merge branch-name
```

### 环境管理
```cmd
# 查看Python版本和路径
python --version
where python

# 虚拟环境
python -m venv venv
venv\Scripts\activate  # 激活虚拟环境
deactivate  # 停用虚拟环境

# 包管理
pip list
pip freeze > requirements.txt
pip install -r requirements.txt
```
