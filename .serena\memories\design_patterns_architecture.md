# 设计模式和架构指导

## 主要设计模式

### 1. 策略模式 (Strategy Pattern)
- **抓取策略**: `ContentScrapingStrategy`, `WebScrapingStrategy`, `LXMLWebScrapingStrategy`
- **提取策略**: `ExtractionStrategy`, `LLMExtractionStrategy`, `JsonCssExtractionStrategy`
- **代理策略**: `ProxyRotationStrategy`, `RoundRobinProxyStrategy`
- **过滤策略**: `ContentFilterStrategy`, `BM25ContentFilter`, `LLMContentFilter`

### 2. 工厂模式 (Factory Pattern)
- 用于创建不同类型的爬虫、策略和配置对象
- 配置类: `BrowserConfig`, `CrawlerRunConfig`, `HTTPCrawlerConfig`

### 3. 异步上下文管理器 (Async Context Manager)
```python
# AsyncWebCrawler实现__aenter__和__aexit__
async with AsyncWebCrawler() as crawler:
    result = await crawler.arun(url)
```

### 4. 单例模式 (Singleton Pattern)
- 浏览器管理器和资源池使用单例模式确保资源合理使用

### 5. 观察者模式 (Observer Pattern)
- 日志系统: `AsyncLogger`, `AsyncLoggerBase`
- 监控系统: `CrawlerMonitor`

## 架构原则

### 异步优先 (Async-First)
- 所有主要操作都是异步的
- 使用 `asyncio` 进行并发处理
- 方法命名约定: 异步方法前缀 'a' (如 `arun`, `aprocess_html`)

### 模块化设计
- 每个功能模块职责单一
- 组件之间通过接口交互
- 易于扩展和测试

### 配置驱动
- 使用配置对象而非大量参数
- 支持环境变量和配置文件
- 配置验证使用pydantic

### 错误处理
- 使用类型化的异常
- 提供详细的错误信息
- 优雅降级和恢复机制

## 核心组件架构

### 1. AsyncWebCrawler
- **职责**: 主要的爬虫引擎
- **模式**: 外观模式(Facade) - 提供简化的API
- **特性**: 
  - 浏览器生命周期管理
  - 会话管理
  - 并发控制

### 2. Browser Manager
- **职责**: 浏览器实例管理
- **模式**: 池化(Pool Pattern)
- **特性**: 
  - 浏览器预热
  - 资源复用
  - 自动清理

### 3. Strategy Classes
- **职责**: 可插拔的处理逻辑
- **模式**: 策略模式
- **扩展**: 实现相应接口即可添加新策略

### 4. Config Classes
- **职责**: 配置管理和验证
- **模式**: 建造者模式 + 数据类
- **验证**: 使用pydantic进行类型和值验证

## 扩展指导

### 添加新的抓取策略
1. 继承 `ContentScrapingStrategy`
2. 实现必需的抽象方法
3. 在 `__init__.py` 中导出
4. 添加相应测试

### 添加新的提取策略
1. 继承 `ExtractionStrategy`
2. 实现 `extract` 方法
3. 处理错误和边界情况
4. 添加配置参数支持

### 添加新的配置选项
1. 在相应的配置类中添加字段
2. 使用pydantic验证器
3. 更新文档
4. 保持向后兼容性

## 性能考虑

### 内存管理
- 及时释放浏览器资源
- 使用弱引用避免循环引用
- 限制并发连接数

### 并发控制
- 使用信号量限制并发
- 域名级别的速率限制
- 自适应负载均衡

### 缓存策略
- 多级缓存 (HTML, 清理后的HTML, Markdown)
- 缓存失效策略
- 压缩存储

## 代码组织原则

### 文件命名
- 模块名: snake_case
- 类名: PascalCase
- 异步模块: async_ 前缀

### 依赖管理
- 核心依赖最小化
- 可选功能使用额外依赖
- 明确的版本约束

### 接口设计
- 简单清晰的公共API
- 丰富的配置选项
- 向后兼容性保证
