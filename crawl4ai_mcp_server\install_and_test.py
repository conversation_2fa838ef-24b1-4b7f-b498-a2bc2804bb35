#!/usr/bin/env python3
"""
Installation and basic test script for Crawl4AI MCP Server.

This script installs the required dependencies and runs basic tests
to ensure the server is working correctly.
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(cmd, description):
    """Run a command and handle errors."""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed!")
        print(f"   Error: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 9:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible!")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible!")
        print("   Requires Python 3.9 or higher")
        return False


def install_dependencies():
    """Install required dependencies."""
    print("\n📦 Installing dependencies...")
    
    # Core dependencies
    dependencies = [
        "mcp>=1.0.0",
        "crawl4ai>=0.6.2", 
        "pydantic>=2.10",
        "aiofiles>=24.1.0",
        "rank-bm25>=0.2.2"
    ]
    
    for dep in dependencies:
        if not run_command(f"pip install '{dep}'", f"Installing {dep}"):
            return False
    
    # Install playwright browsers
    if not run_command("playwright install", "Installing Playwright browsers"):
        print("⚠️  Playwright browser installation failed - some features may not work")
    
    return True


def test_imports():
    """Test that all required modules can be imported."""
    print("\n🔍 Testing imports...")
    
    imports_to_test = [
        ("mcp", "MCP library"),
        ("crawl4ai", "Crawl4AI library"),
        ("pydantic", "Pydantic library"),
        ("aiofiles", "AIOFiles library"),
        ("rank_bm25", "BM25 library")
    ]
    
    all_imports_ok = True
    
    for module, description in imports_to_test:
        try:
            __import__(module)
            print(f"✅ {description} import successful")
        except ImportError as e:
            print(f"❌ {description} import failed: {e}")
            all_imports_ok = False
    
    return all_imports_ok


def test_server_startup():
    """Test that the server can start up."""
    print("\n🚀 Testing server startup...")
    
    # Change to the src directory
    src_dir = Path(__file__).parent / "src"
    original_cwd = os.getcwd()
    
    try:
        os.chdir(src_dir)
        
        # Try to import the server module
        sys.path.insert(0, str(src_dir))
        
        try:
            from crawl4ai_mcp.server import server, TOOLS
            print(f"✅ Server module imported successfully!")
            print(f"   Available tools: {list(TOOLS.keys())}")
            return True
        except ImportError as e:
            print(f"❌ Server module import failed: {e}")
            return False
        
    finally:
        os.chdir(original_cwd)
        if str(src_dir) in sys.path:
            sys.path.remove(str(src_dir))


def main():
    """Main installation and test function."""
    print("🛠️  Crawl4AI MCP Server Installation & Test")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Dependency installation failed!")
        return False
    
    # Test imports
    if not test_imports():
        print("\n❌ Import tests failed!")
        return False
    
    # Test server startup
    if not test_server_startup():
        print("\n❌ Server startup test failed!")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 Installation and basic tests completed successfully!")
    print("\n📋 Next steps:")
    print("   1. Run the server: python -m crawl4ai_mcp.server")
    print("   2. Or run full tests: python test_server.py")
    print("   3. Connect your MCP client to the server")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)