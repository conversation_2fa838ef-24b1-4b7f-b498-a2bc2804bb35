# 技术栈和依赖

## 编程语言
- **主要语言**: Python 3.9+
- 支持版本: Python 3.9, 3.10, 3.11, 3.12, 3.13

## 核心依赖
- **playwright>=1.49.0**: 浏览器自动化和网页抓取
- **beautifulsoup4~=4.12**: HTML解析
- **lxml~=5.3**: XML/HTML处理
- **aiosqlite~=0.20**: 异步SQLite数据库
- **litellm>=1.53.1**: LLM集成
- **requests~=2.26**: HTTP请求
- **pydantic>=2.10**: 数据验证和设置
- **aiofiles>=24.1.0**: 异步文件操作
- **rich>=13.9.4**: 终端美化输出
- **click>=8.1.7**: 命令行界面

## 可选依赖包
- **pdf**: PyPDF2 (PDF处理)
- **torch**: torch, nltk, scikit-learn (机器学习)
- **transformer**: transformers, tokenizers (Transformer模型)
- **cosine**: torch, transformers, nltk (余弦相似度)
- **sync**: selenium (同步版本支持)
- **all**: 包含所有可选依赖

## 开发工具
- **pytest**: 测试框架
- **black**: 代码格式化
- **setuptools**: 包构建
- **mkdocs**: 文档生成
