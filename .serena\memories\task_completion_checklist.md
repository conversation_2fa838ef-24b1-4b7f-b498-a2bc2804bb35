# 任务完成后的流程

## 代码质量检查

### 1. 代码格式化
```cmd
# 使用black格式化所有修改的代码
python -m black crawl4ai/
python -m black tests/

# 检查格式化是否正确
python -m black --check crawl4ai/
```

### 2. 代码检查
```cmd
# 运行代码质量检查
python -m flake8 crawl4ai/

# 安全检查
python -m bandit -r crawl4ai/
```

## 测试验证

### 3. 运行测试套件
```cmd
# 运行所有测试
python -m pytest tests/

# 运行相关的特定测试
python -m pytest tests/async/  # 异步功能测试
python -m pytest tests/browser/  # 浏览器相关测试

# 运行测试并生成覆盖率报告
python -m pytest tests/ --cov=crawl4ai --cov-report=html
```

### 4. 集成测试
```cmd
# 验证安装
crawl4ai-doctor

# 测试CLI功能
crwl https://example.com -o markdown

# 测试主要功能
python -c "
import asyncio
from crawl4ai import AsyncWebCrawler

async def test():
    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun('https://example.com')
        print('Test passed!' if result.success else 'Test failed!')

asyncio.run(test())
"
```

## 文档更新

### 5. 更新文档
```cmd
# 如果修改了API，更新文档
mkdocs serve  # 本地预览文档

# 生成API文档
python -c "import crawl4ai; help(crawl4ai.AsyncWebCrawler)"
```

### 6. 更新CHANGELOG
- 在 `CHANGELOG.md` 中记录重要更改
- 按照 Keep a Changelog 格式
- 包含版本号、日期和更改类型(Added/Changed/Fixed/Removed)

## 版本控制

### 7. Git提交
```cmd
# 检查状态
git status

# 添加更改
git add .

# 提交更改(使用描述性消息)
git commit -m "type(scope): description

详细说明更改内容和原因"

# 推送更改
git push
```

### 8. 版本标记(如果是发布)
```cmd
# 创建版本标签
git tag -a v0.6.2 -m "Release version 0.6.2"
git push origin v0.6.2
```

## 性能和内存检查

### 9. 性能测试
```cmd
# 运行性能测试
python -m pytest tests/memory/ -v

# 内存测试
python tests/memory/test_memory_usage.py
```

### 10. 浏览器资源清理
```cmd
# 确保浏览器实例正确关闭
python -c "
import asyncio
from crawl4ai import AsyncWebCrawler

async def cleanup_test():
    crawler = AsyncWebCrawler()
    await crawler.start()
    await crawler.close()
    print('Cleanup test passed!')

asyncio.run(cleanup_test())
"
```

## 部署前检查

### 11. Docker测试(如果相关)
```cmd
# 构建Docker镜像
docker build -t crawl4ai-test .

# 运行容器测试
docker run --rm crawl4ai-test python -c "import crawl4ai; print('Docker test passed!')"
```

### 12. 依赖检查
```cmd
# 检查依赖兼容性
pip check

# 更新requirements.txt(如果有新依赖)
pip freeze > requirements.txt
```

## 清理工作

### 13. 清理临时文件
```cmd
# 清理Python缓存
find . -type d -name "__pycache__" -exec rmdir /s {} +  # Windows
find . -name "*.pyc" -delete

# 清理测试生成的文件
del /q /s tests\*.tmp  # Windows
```

### 14. 验证安装包
```cmd
# 构建分发包
python -m build

# 检查包
python -m twine check dist/*
```
