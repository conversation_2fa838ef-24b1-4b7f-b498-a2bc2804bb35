"""Configuration management for Crawl4AI MCP Server."""

import os
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field


class LLMConfig(BaseModel):
    """LLM configuration for content filtering."""
    provider: str = Field(default="openai", description="LLM provider")
    api_token: Optional[str] = Field(default=None, description="API token")
    model: str = Field(default="gpt-3.5-turbo", description="Model name")


class ServerConfig(BaseModel):
    """Main server configuration."""
    name: str = Field(default="crawl4ai-mcp", description="Server name")
    version: str = Field(default="0.1.0", description="Server version")
    
    # LLM configuration
    llm: LLMConfig = Field(default_factory=LLMConfig)
    
    # Default settings
    default_cache_mode: str = Field(default="write_only", description="Default cache mode")
    max_concurrent_crawls: int = Field(default=5, description="Max concurrent crawl operations")


def load_config() -> ServerConfig:
    """Load configuration from environment variables and defaults."""
    
    # Load LLM config from environment
    llm_config = LLMConfig(
        provider=os.getenv("CRAWL4AI_LLM_PROVIDER", "openai"),
        api_token=os.getenv("OPENAI_API_KEY") or os.getenv("ANTHROPIC_API_KEY"),
        model=os.getenv("CRAWL4AI_LLM_MODEL", "gpt-3.5-turbo")
    )
    
    return ServerConfig(
        name=os.getenv("CRAWL4AI_MCP_NAME", "crawl4ai-mcp"),
        version=os.getenv("CRAWL4AI_MCP_VERSION", "0.1.0"),
        llm=llm_config,
        default_cache_mode=os.getenv("CRAWL4AI_CACHE_MODE", "write_only"),
        max_concurrent_crawls=int(os.getenv("CRAWL4AI_MAX_CONCURRENT", "5"))
    )


# Global config instance
config = load_config()