# Crawl4AI MCP Server - Quick Start Guide

Get up and running with the Crawl4AI MCP Server in minutes!

## 🚀 Quick Installation

```bash
# 1. Navigate to the server directory
cd crawl4ai_mcp_server

# 2. Install the package
pip install -e .

# 3. Install Playwright browsers (required for crawling)
playwright install

# 4. Test the installation
python validate_syntax.py
```

## ⚡ Quick Test

```bash
# Start the server (it will wait for MCP client connection)
python -m crawl4ai_mcp.server

# In another terminal, test with a simple MCP client
python test_server.py
```

## 🔧 Environment Setup (Optional)

```bash
# For LLM-based content filtering
export OPENAI_API_KEY="your-openai-key"
export CRAWL4AI_LLM_PROVIDER="openai"

# For Anthropic
export ANTHROPIC_API_KEY="your-anthropic-key" 
export CRAWL4AI_LLM_PROVIDER="anthropic"
```

## 📱 Connect to <PERSON>

Add to your Claude Desktop MCP configuration (`claude_desktop_config.json`):

```json
{
  "mcpServers": {
    "crawl4ai": {
      "command": "python",
      "args": ["-m", "crawl4ai_mcp.server"],
      "cwd": "/path/to/crawl4ai_mcp_server/src"
    }
  }
}
```

## 🛠️ Basic Usage Examples

### Generate Markdown
```python
# In your MCP client
result = await session.call_tool("md", {
    "url": "https://example.com",
    "f": "fit"  # Clean, filtered content
})
```

### Capture Screenshot
```python
result = await session.call_tool("screenshot", {
    "url": "https://example.com",
    "screenshot_wait_for": 2
})
```

### Multi-URL Crawling
```python
result = await session.call_tool("crawl", {
    "urls": ["https://site1.com", "https://site2.com"]
})
```

## 🔍 Available Tools

| Tool | Description | Key Parameters |
|------|-------------|----------------|
| `md` | Generate markdown | `url`, `f` (filter), `q` (query) |
| `html` | Extract HTML | `url` |
| `crawl` | Multi-URL crawling | `urls`, `browser_config`, `crawler_config` |
| `screenshot` | Capture screenshots | `url`, `screenshot_wait_for` |
| `pdf` | Generate PDFs | `url`, `output_path` |
| `execute_js` | Run JavaScript | `url`, `js_code` |
| `ask` | Query library docs | `query`, `context_type` |

## 🚨 Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   pip install crawl4ai>=0.6.2 mcp>=1.0.0
   ```

2. **Browser Not Found**
   ```bash
   playwright install
   ```

3. **Permission Errors**
   - Ensure write permissions for output directories
   - Check file paths are absolute

4. **LLM Errors**
   - Verify API keys are set
   - Check provider configuration

### Debug Mode

```bash
# Enable debug logging
export PYTHONPATH=.
python -c "import logging; logging.basicConfig(level=logging.DEBUG)"
python -m crawl4ai_mcp.server
```

## 📚 Next Steps

- Read the full [README.md](README.md) for detailed documentation
- Check out the [test_server.py](test_server.py) for usage examples
- Explore the tool implementations in `src/crawl4ai_mcp/tools/`

## 🆘 Need Help?

- Check the original Crawl4AI documentation
- Review the MCP protocol specification
- Look at the tool source code for parameter details