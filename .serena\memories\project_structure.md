# 项目结构

## 根目录结构
```
crawl4ai-origin/
├── .github/              # GitHub配置文件 (工作流、issue模板)
├── .serena/              # Serena代理配置
├── crawl4ai/             # 主要源代码包
├── deploy/               # 部署相关文件
├── docs/                 # 文档目录
├── prompts/              # 提示词文件
├── tests/                # 测试文件
├── pyproject.toml        # 项目配置(主要)
├── setup.py              # 安装脚本
├── requirements.txt      # 依赖列表
├── README.md             # 项目说明
├── CHANGELOG.md          # 版本更新日志
├── Dockerfile            # Docker配置
├── docker-compose.yml    # Docker Compose配置
└── mkdocs.yml            # 文档配置
```

## 主要源代码结构 (crawl4ai/)
```
crawl4ai/
├── components/           # 组件目录
├── crawlers/             # 爬虫实现
├── deep_crawling/        # 深度爬取功能
├── html2text/            # HTML转文本功能
├── js_snippet/           # JavaScript代码片段
├── legacy/               # 遗留代码
├── processors/           # 数据处理器
├── async_webcrawler.py   # 主要异步爬虫类
├── async_configs.py      # 异步配置
├── browser_manager.py    # 浏览器管理
├── cli.py                # 命令行接口
├── models.py             # 数据模型
├── utils.py              # 实用工具
└── __init__.py           # 包初始化和API定义
```

## 测试结构 (tests/)
```
tests/
├── async/                # 异步功能测试
├── browser/              # 浏览器相关测试
├── cli/                  # CLI测试
├── docker/               # Docker测试
├── general/              # 通用测试
├── hub/                  # Hub功能测试
├── loggers/              # 日志测试
├── mcp/                  # MCP协议测试
├── memory/               # 内存测试
└── profiler/             # 性能分析测试
```

## 文档结构 (docs/)
```
docs/
├── apps/                 # 应用示例
├── assets/               # 资源文件
├── codebase/             # 代码库文档
├── examples/             # 示例代码
├── tutorials/            # 教程
└── md_v2/                # Markdown v2文档
```
