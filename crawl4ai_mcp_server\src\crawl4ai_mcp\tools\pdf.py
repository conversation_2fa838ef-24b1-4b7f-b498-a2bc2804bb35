"""PDF generation tool for Crawl4AI MCP Server."""

import os
from typing import Dict, Any, List, Optional

import mcp.types as types
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig

from ..utils import create_json_response, create_error_response, validate_url, create_tool_schema


class PDFTool:
    """Tool for generating PDF documents from web pages."""
    
    @staticmethod
    def get_schema() -> types.Tool:
        """Get the tool schema for MCP."""
        return types.Tool(
            name="pdf",
            description="Generate a PDF document from the specified URL",
            inputSchema=create_tool_schema(
                properties={
                    "url": {
                        "type": "string",
                        "description": "Absolute http/https URL to convert to PDF"
                    },
                    "output_path": {
                        "type": "string",
                        "description": "Optional path to save PDF file"
                    },
                    "pdf_wait_for": {
                        "type": "number",
                        "default": 2,
                        "description": "Time to wait before generating PDF (seconds)"
                    }
                },
                required=["url"]
            )
        )
    
    @staticmethod
    async def execute(arguments: Dict[str, Any]) -> List[types.TextContent]:
        """Execute the PDF generation tool."""
        try:
            # Extract and validate arguments
            url = arguments.get("url")
            if not url:
                return create_error_response("URL is required", 400)
            
            output_path = arguments.get("output_path")
            pdf_wait_for = arguments.get("pdf_wait_for", 2)
            
            # Validate URL
            try:
                validated_url = validate_url(url)
            except Exception as e:
                return create_error_response(f"Invalid URL: {str(e)}", 400)
            
            # Generate PDF
            pdf_data = await PDFTool._generate_pdf(
                validated_url, output_path, pdf_wait_for
            )
            
            # Return response
            response_data = {
                "url": validated_url,
                "pdf": pdf_data,
                "success": True
            }
            
            if output_path:
                response_data["output_path"] = output_path
            
            return create_json_response(response_data)
            
        except Exception as e:
            return create_error_response(f"PDF generation failed: {str(e)}")
    
    @staticmethod
    async def _generate_pdf(
        url: str,
        output_path: Optional[str] = None,
        wait_time: float = 2
    ) -> str:
        """Generate PDF and return as base64."""
        
        # Configure crawler for PDF generation
        crawler_config = CrawlerRunConfig(
            pdf=True,
            pdf_wait_for=wait_time
        )
        
        async with AsyncWebCrawler(config=BrowserConfig()) as crawler:
            results = await crawler.arun(url=url, config=crawler_config)
            
            if not results or not results[0].success:
                error_msg = results[0].error_message if results else "PDF generation failed"
                raise Exception(error_msg)
            
            pdf_data = results[0].pdf
            if not pdf_data:
                raise Exception("No PDF data returned")
            
            # Save to file if output_path provided
            if output_path:
                try:
                    # Ensure directory exists
                    os.makedirs(os.path.dirname(output_path), exist_ok=True)
                    
                    # Decode base64 and save
                    import base64
                    pdf_bytes = base64.b64decode(pdf_data)
                    with open(output_path, 'wb') as f:
                        f.write(pdf_bytes)
                except Exception as e:
                    raise Exception(f"Failed to save PDF to {output_path}: {str(e)}")
            
            return pdf_data