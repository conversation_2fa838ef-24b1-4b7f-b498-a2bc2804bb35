#!/usr/bin/env python3
"""Quick syntax check for the MCP server files."""

import ast
import sys
from pathlib import Path

def check_file(file_path):
    """Check syntax of a Python file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        ast.parse(content)
        print(f"✅ {file_path.name}")
        return True
    except SyntaxError as e:
        print(f"❌ {file_path.name}: Line {e.lineno}: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ {file_path.name}: {e}")
        return False

# Check key files
files_to_check = [
    "src/crawl4ai_mcp/server.py",
    "src/crawl4ai_mcp/tools/context.py",
    "src/crawl4ai_mcp/config.py",
    "src/crawl4ai_mcp/utils.py"
]

print("🔍 Checking syntax of key files...")
all_good = True

for file_path in files_to_check:
    path = Path(file_path)
    if path.exists():
        if not check_file(path):
            all_good = False
    else:
        print(f"❌ File not found: {file_path}")
        all_good = False

if all_good:
    print("\n✅ All files have valid syntax!")
else:
    print("\n❌ Some files have syntax errors!")

sys.exit(0 if all_good else 1)