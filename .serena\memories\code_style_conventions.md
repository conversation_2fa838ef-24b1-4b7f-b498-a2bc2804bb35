# 代码风格和约定

## 格式化工具
- **black**: 主要代码格式化工具
- 项目徽章显示"code style: black"

## 命名约定
- **类名**: PascalCase (如 AsyncWebCrawler, BrowserConfig)
- **方法名**: snake_case (如 arun, aprocess_html)
- **常量**: UPPER_SNAKE_CASE
- **私有成员**: 前缀下划线 (如 _domain_last_hit, _lock)
- **异步方法**: 前缀 'a' (如 arun, aenter, aexit)

## 文档字符串
- 使用三引号格式
- 包含参数和返回值说明
- 遵循Python标准文档约定

## 类型注释
- 广泛使用类型提示
- 使用pydantic进行数据验证
- 导入类型: `from typing import Dict, Any, Optional, List`

## 文件组织
- **主包**: crawl4ai/
- **测试**: tests/
- **文档**: docs/
- **配置**: pyproject.toml (主要), setup.py (向后兼容)

## 代码结构模式
- 使用异步编程模式 (async/await)
- 策略模式用于不同的抓取策略
- 工厂模式用于组件创建
- 上下文管理器用于资源管理 (__aenter__, __aexit__)

## 导入约定
- 使用相对导入在包内
- 按标准库、第三方库、本地导入顺序组织
- 使用 __all__ 定义公共API
