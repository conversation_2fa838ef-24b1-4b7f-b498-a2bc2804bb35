#!/usr/bin/env python3
"""Test basic imports for the MCP server."""

import sys
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_import(module_name, description):
    """Test importing a module."""
    try:
        __import__(module_name)
        print(f"✅ {description}")
        return True
    except ImportError as e:
        print(f"❌ {description}: {e}")
        return False
    except Exception as e:
        print(f"❌ {description}: Unexpected error: {e}")
        return False

print("🔍 Testing imports...")

tests = [
    ("crawl4ai_mcp", "Main package"),
    ("crawl4ai_mcp.config", "Configuration module"),
    ("crawl4ai_mcp.utils", "Utilities module"),
    ("crawl4ai_mcp.tools", "Tools package"),
    ("crawl4ai_mcp.tools.markdown", "Markdown tool"),
    ("crawl4ai_mcp.tools.context", "Context tool"),
]

all_passed = True
for module, desc in tests:
    if not test_import(module, desc):
        all_passed = False

if all_passed:
    print("\n✅ All imports successful!")
    
    # Try to access the server
    try:
        from crawl4ai_mcp.server import server, TOOLS
        print(f"✅ Server object created successfully")
        print(f"✅ Available tools: {list(TOOLS.keys())}")
    except Exception as e:
        print(f"❌ Server creation failed: {e}")
        all_passed = False
else:
    print("\n❌ Some imports failed!")

sys.exit(0 if all_passed else 1)