"""Screenshot capture tool for Crawl4AI MCP Server."""

import os
from typing import Dict, Any, List, Optional

import mcp.types as types
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig

from ..utils import create_json_response, create_error_response, validate_url, create_tool_schema, encode_binary_to_base64


class ScreenshotTool:
    """Tool for capturing webpage screenshots."""
    
    @staticmethod
    def get_schema() -> types.Tool:
        """Get the tool schema for MCP."""
        return types.Tool(
            name="screenshot",
            description="Capture a full-page PNG screenshot of the specified URL",
            inputSchema=create_tool_schema(
                properties={
                    "url": {
                        "type": "string",
                        "description": "Absolute http/https URL to capture"
                    },
                    "screenshot_wait_for": {
                        "type": "number",
                        "default": 2,
                        "description": "Time to wait before taking screenshot (seconds)"
                    },
                    "output_path": {
                        "type": "string",
                        "description": "Optional path to save screenshot file"
                    }
                },
                required=["url"]
            )
        )
    
    @staticmethod
    async def execute(arguments: Dict[str, Any]) -> List[types.TextContent]:
        """Execute the screenshot tool."""
        try:
            # Extract and validate arguments
            url = arguments.get("url")
            if not url:
                return create_error_response("URL is required", 400)
            
            screenshot_wait_for = arguments.get("screenshot_wait_for", 2)
            output_path = arguments.get("output_path")
            
            # Validate URL
            try:
                validated_url = validate_url(url)
            except Exception as e:
                return create_error_response(f"Invalid URL: {str(e)}", 400)
            
            # Capture screenshot
            screenshot_data = await ScreenshotTool._capture_screenshot(
                validated_url, screenshot_wait_for, output_path
            )
            
            # Return response
            response_data = {
                "url": validated_url,
                "screenshot": screenshot_data,
                "success": True
            }
            
            if output_path:
                response_data["output_path"] = output_path
            
            return create_json_response(response_data)
            
        except Exception as e:
            return create_error_response(f"Screenshot capture failed: {str(e)}")
    
    @staticmethod
    async def _capture_screenshot(
        url: str,
        wait_time: float = 2,
        output_path: Optional[str] = None
    ) -> str:
        """Capture screenshot and return as base64."""
        
        # Configure crawler for screenshot
        crawler_config = CrawlerRunConfig(
            screenshot=True,
            screenshot_wait_for=wait_time
        )
        
        async with AsyncWebCrawler(config=BrowserConfig()) as crawler:
            results = await crawler.arun(url=url, config=crawler_config)
            
            if not results or not results[0].success:
                error_msg = results[0].error_message if results else "Screenshot capture failed"
                raise Exception(error_msg)
            
            screenshot_data = results[0].screenshot
            if not screenshot_data:
                raise Exception("No screenshot data returned")
            
            # Save to file if output_path provided
            if output_path:
                try:
                    # Ensure directory exists
                    os.makedirs(os.path.dirname(output_path), exist_ok=True)
                    
                    # Decode base64 and save
                    import base64
                    screenshot_bytes = base64.b64decode(screenshot_data)
                    with open(output_path, 'wb') as f:
                        f.write(screenshot_bytes)
                except Exception as e:
                    raise Exception(f"Failed to save screenshot to {output_path}: {str(e)}")
            
            return screenshot_data