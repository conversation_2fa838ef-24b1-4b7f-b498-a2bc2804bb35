#!/usr/bin/env python3
"""
Crawl4AI MCP Server - Standalone executable script.

This script provides a Model Control Protocol (MCP) server for Crawl4AI functionality
without requiring Docker or complex module installation. It can be run directly with:
    python standalone_server.py

Requirements:
    pip install mcp crawl4ai>=0.6.2 pydantic>=2.10 aiofiles>=24.1.0 rank-bm25>=0.2.2
    playwright install
"""

import asyncio
import json
import logging
import os
import re
import base64
from typing import Dict, Any, List, Optional, Union
from enum import Enum
from pathlib import Path

# MCP imports
from mcp.server.models import InitializationOptions
import mcp.types as types
from mcp.server import NotificationOptions, Server
import mcp.server.stdio

# Crawl4AI imports
from crawl4ai import (
    AsyncWebCrawler, 
    CrawlerRunConfig, 
    CacheMode,
    BrowserConfig,
    LLMConfig,
    <PERSON>runing<PERSON>ontent<PERSON>ilter, 
    BM25<PERSON>ontentFilter, 
    LLMContent<PERSON>ilt<PERSON>,
    DefaultMarkdownGenerator,
    LXMLWebScrapingStrategy
)
from crawl4ai.utils import preprocess_html_for_schema

# BM25 for context search
try:
    from rank_bm25 import BM25<PERSON>kapi
except ImportError:
    print("Warning: rank-bm25 not installed. Context search will use simple text matching.")
    BM25Okapi = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
class ServerConfig:
    """Server configuration."""
    def __init__(self):
        self.name = os.getenv("CRAWL4AI_MCP_NAME", "crawl4ai-mcp")
        self.version = os.getenv("CRAWL4AI_MCP_VERSION", "0.1.0")
        self.llm_provider = os.getenv("CRAWL4AI_LLM_PROVIDER", "openai")
        self.llm_api_token = os.getenv("OPENAI_API_KEY") or os.getenv("ANTHROPIC_API_KEY")
        self.llm_model = os.getenv("CRAWL4AI_LLM_MODEL", "gpt-3.5-turbo")
        self.default_cache_mode = os.getenv("CRAWL4AI_CACHE_MODE", "write_only")
        self.max_concurrent = int(os.getenv("CRAWL4AI_MAX_CONCURRENT", "5"))

config = ServerConfig()

# Utility functions
def create_text_content(text: str) -> types.TextContent:
    """Create MCP TextContent from string."""
    return types.TextContent(type="text", text=text)

def create_json_response(data: Any) -> List[types.TextContent]:
    """Create MCP response with JSON data."""
    return [create_text_content(json.dumps(data, default=str))]

def create_error_response(error: str, status_code: int = 500) -> List[types.TextContent]:
    """Create MCP error response."""
    error_data = {
        "error": status_code,
        "detail": error,
        "success": False
    }
    return [create_text_content(json.dumps(error_data))]

def validate_url(url: str) -> str:
    """Validate and normalize URL."""
    from urllib.parse import unquote
    decoded_url = unquote(url)
    if not decoded_url.startswith(('http://', 'https://')):
        decoded_url = 'https://' + decoded_url
    return decoded_url

def create_tool_schema(properties: Dict[str, Any], required: List[str]) -> Dict[str, Any]:
    """Create JSON schema for MCP tool."""
    return {
        "type": "object",
        "properties": properties,
        "required": required
    }

# Filter types
class FilterType(str, Enum):
    """Content filter types."""
    RAW = "raw"
    FIT = "fit" 
    BM25 = "bm25"
    LLM = "llm"

# Create the MCP server
server = Server(config.name)

# Tool implementations
class MarkdownTool:
    """Tool for generating markdown from web content."""
    
    @staticmethod
    def get_schema() -> types.Tool:
        """Get the tool schema for MCP."""
        return types.Tool(
            name="md",
            description="Generate markdown from web content with various filtering options",
            inputSchema=create_tool_schema(
                properties={
                    "url": {
                        "type": "string",
                        "description": "Absolute http/https URL to fetch"
                    },
                    "f": {
                        "type": "string", 
                        "enum": ["raw", "fit", "bm25", "llm"],
                        "default": "fit",
                        "description": "Content filter strategy: fit, raw, bm25, or llm"
                    },
                    "q": {
                        "type": "string",
                        "description": "Query string used by BM25/LLM filters"
                    },
                    "c": {
                        "type": "string",
                        "default": "0", 
                        "description": "Cache-bust / revision counter"
                    }
                },
                required=["url"]
            )
        )
    
    @staticmethod
    async def execute(arguments: Dict[str, Any]) -> List[types.TextContent]:
        """Execute the markdown generation tool."""
        try:
            # Extract and validate arguments
            url = arguments.get("url")
            if not url:
                return create_error_response("URL is required", 400)
            
            filter_type = FilterType(arguments.get("f", "fit"))
            query = arguments.get("q")
            cache = arguments.get("c", "0")
            
            # Validate URL
            try:
                validated_url = validate_url(url)
            except Exception as e:
                return create_error_response(f"Invalid URL: {str(e)}", 400)
            
            # Generate markdown
            markdown = await MarkdownTool._generate_markdown(
                validated_url, filter_type, query, cache
            )
            
            # Return response
            response_data = {
                "url": validated_url,
                "filter": filter_type.value,
                "query": query,
                "cache": cache,
                "markdown": markdown,
                "success": True
            }
            
            return create_json_response(response_data)
            
        except Exception as e:
            return create_error_response(f"Markdown generation failed: {str(e)}")
    
    @staticmethod
    async def _generate_markdown(
        url: str,
        filter_type: FilterType,
        query: Optional[str] = None,
        cache: str = "0"
    ) -> str:
        """Generate markdown from URL with specified filter."""
        
        # Configure markdown generator based on filter type
        if filter_type == FilterType.RAW:
            md_generator = DefaultMarkdownGenerator()
        else:
            content_filter = {
                FilterType.FIT: PruningContentFilter(),
                FilterType.BM25: BM25ContentFilter(user_query=query or ""),
                FilterType.LLM: LLMContentFilter(
                    llm_config=LLMConfig(
                        provider=config.llm_provider,
                        api_token=config.llm_api_token or "",
                    ),
                    instruction=query or "Extract main content"
                )
            }[filter_type]
            md_generator = DefaultMarkdownGenerator(content_filter=content_filter)
        
        # Configure cache mode
        cache_mode = CacheMode.ENABLED if cache == "1" else CacheMode.WRITE_ONLY
        
        # Crawl and generate markdown
        async with AsyncWebCrawler() as crawler:
            result = await crawler.arun(
                url=url,
                config=CrawlerRunConfig(
                    markdown_generator=md_generator,
                    scraping_strategy=LXMLWebScrapingStrategy(),
                    cache_mode=cache_mode
                )
            )
            
            if not result.success:
                raise Exception(result.error_message or "Crawl failed")
            
            return (result.markdown.raw_markdown 
                   if filter_type == FilterType.RAW 
                   else result.markdown.fit_markdown)

class HTMLTool:
    """Tool for extracting preprocessed HTML from web pages."""
    
    @staticmethod
    def get_schema() -> types.Tool:
        """Get the tool schema for MCP."""
        return types.Tool(
            name="html",
            description="Crawls the URL, preprocesses the raw HTML for schema extraction, and returns the processed HTML. Use when you need sanitized HTML structures for building schemas or further processing.",
            inputSchema=create_tool_schema(
                properties={
                    "url": {
                        "type": "string",
                        "description": "Absolute http/https URL to fetch"
                    }
                },
                required=["url"]
            )
        )
    
    @staticmethod
    async def execute(arguments: Dict[str, Any]) -> List[types.TextContent]:
        """Execute the HTML extraction tool."""
        try:
            # Extract and validate arguments
            url = arguments.get("url")
            if not url:
                return create_error_response("URL is required", 400)
            
            # Validate URL
            try:
                validated_url = validate_url(url)
            except Exception as e:
                return create_error_response(f"Invalid URL: {str(e)}", 400)
            
            # Extract HTML
            processed_html = await HTMLTool._extract_html(validated_url)
            
            # Return response
            response_data = {
                "html": processed_html,
                "url": validated_url,
                "success": True
            }
            
            return create_json_response(response_data)
            
        except Exception as e:
            return create_error_response(f"HTML extraction failed: {str(e)}")
    
    @staticmethod
    async def _extract_html(url: str) -> str:
        """Extract and preprocess HTML from URL."""
        cfg = CrawlerRunConfig()
        async with AsyncWebCrawler(config=BrowserConfig()) as crawler:
            results = await crawler.arun(url=url, config=cfg)
        
        if not results or not results[0].success:
            error_msg = results[0].error_message if results else "Crawl failed"
            raise Exception(error_msg)
        
        raw_html = results[0].html
        processed_html = preprocess_html_for_schema(raw_html)
        return processed_html

class CrawlTool:
    """Tool for crawling multiple URLs and returning detailed results."""

    @staticmethod
    def get_schema() -> types.Tool:
        """Get the tool schema for MCP."""
        return types.Tool(
            name="crawl",
            description="Crawl a list of URLs and return the results as JSON with full crawl details",
            inputSchema=create_tool_schema(
                properties={
                    "urls": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of URLs to crawl"
                    },
                    "browser_config": {
                        "type": "object",
                        "description": "Browser configuration options",
                        "default": {}
                    },
                    "crawler_config": {
                        "type": "object",
                        "description": "Crawler configuration options",
                        "default": {}
                    }
                },
                required=["urls"]
            )
        )

    @staticmethod
    async def execute(arguments: Dict[str, Any]) -> List[types.TextContent]:
        """Execute the crawl tool."""
        try:
            # Extract and validate arguments
            urls = arguments.get("urls", [])
            if not urls:
                return create_error_response("At least one URL is required", 400)

            browser_config_dict = arguments.get("browser_config", {})
            crawler_config_dict = arguments.get("crawler_config", {})

            # Perform crawl
            results = await CrawlTool._crawl_urls(
                urls, browser_config_dict, crawler_config_dict
            )

            return create_json_response(results)

        except Exception as e:
            return create_error_response(f"Crawl failed: {str(e)}")

    @staticmethod
    async def _crawl_urls(
        urls: List[str],
        browser_config: Dict[str, Any],
        crawler_config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Crawl multiple URLs and return results."""

        # Create browser and crawler configs
        browser_cfg = BrowserConfig(**browser_config) if browser_config else BrowserConfig()
        crawler_cfg = CrawlerRunConfig(**crawler_config) if crawler_config else CrawlerRunConfig()

        results = []

        async with AsyncWebCrawler(config=browser_cfg) as crawler:
            for url in urls:
                try:
                    # Validate URL
                    if not url.startswith(('http://', 'https://')):
                        url = 'https://' + url

                    # Crawl the URL
                    crawl_results = await crawler.arun(url=url, config=crawler_cfg)

                    if crawl_results and len(crawl_results) > 0:
                        # Convert CrawlResult to dict for JSON serialization
                        result_dict = crawl_results[0].model_dump()
                        results.append(result_dict)
                    else:
                        results.append({
                            "url": url,
                            "success": False,
                            "error_message": "No results returned",
                            "html": "",
                            "markdown": None
                        })

                except Exception as e:
                    results.append({
                        "url": url,
                        "success": False,
                        "error_message": str(e),
                        "html": "",
                        "markdown": None
                    })

        return results

class ScreenshotTool:
    """Tool for capturing webpage screenshots."""

    @staticmethod
    def get_schema() -> types.Tool:
        """Get the tool schema for MCP."""
        return types.Tool(
            name="screenshot",
            description="Capture a full-page PNG screenshot of the specified URL",
            inputSchema=create_tool_schema(
                properties={
                    "url": {
                        "type": "string",
                        "description": "Absolute http/https URL to capture"
                    },
                    "screenshot_wait_for": {
                        "type": "number",
                        "default": 2,
                        "description": "Time to wait before taking screenshot (seconds)"
                    },
                    "output_path": {
                        "type": "string",
                        "description": "Optional path to save screenshot file"
                    }
                },
                required=["url"]
            )
        )

    @staticmethod
    async def execute(arguments: Dict[str, Any]) -> List[types.TextContent]:
        """Execute the screenshot tool."""
        try:
            # Extract and validate arguments
            url = arguments.get("url")
            if not url:
                return create_error_response("URL is required", 400)

            screenshot_wait_for = arguments.get("screenshot_wait_for", 2)
            output_path = arguments.get("output_path")

            # Validate URL
            try:
                validated_url = validate_url(url)
            except Exception as e:
                return create_error_response(f"Invalid URL: {str(e)}", 400)

            # Capture screenshot
            screenshot_data = await ScreenshotTool._capture_screenshot(
                validated_url, screenshot_wait_for, output_path
            )

            # Return response
            response_data = {
                "url": validated_url,
                "screenshot": screenshot_data,
                "success": True
            }

            if output_path:
                response_data["output_path"] = output_path

            return create_json_response(response_data)

        except Exception as e:
            return create_error_response(f"Screenshot capture failed: {str(e)}")

    @staticmethod
    async def _capture_screenshot(
        url: str,
        wait_time: float = 2,
        output_path: Optional[str] = None
    ) -> str:
        """Capture screenshot and return as base64."""

        # Configure crawler for screenshot
        crawler_config = CrawlerRunConfig(
            screenshot=True,
            screenshot_wait_for=wait_time
        )

        async with AsyncWebCrawler(config=BrowserConfig()) as crawler:
            results = await crawler.arun(url=url, config=crawler_config)

            if not results or not results[0].success:
                error_msg = results[0].error_message if results else "Screenshot capture failed"
                raise Exception(error_msg)

            screenshot_data = results[0].screenshot
            if not screenshot_data:
                raise Exception("No screenshot data returned")

            # Save to file if output_path provided
            if output_path:
                try:
                    # Ensure directory exists
                    os.makedirs(os.path.dirname(output_path), exist_ok=True)

                    # Decode base64 and save
                    screenshot_bytes = base64.b64decode(screenshot_data)
                    with open(output_path, 'wb') as f:
                        f.write(screenshot_bytes)
                except Exception as e:
                    raise Exception(f"Failed to save screenshot to {output_path}: {str(e)}")

            return screenshot_data

class PDFTool:
    """Tool for generating PDF documents from web pages."""

    @staticmethod
    def get_schema() -> types.Tool:
        """Get the tool schema for MCP."""
        return types.Tool(
            name="pdf",
            description="Generate a PDF document from the specified URL",
            inputSchema=create_tool_schema(
                properties={
                    "url": {
                        "type": "string",
                        "description": "Absolute http/https URL to convert to PDF"
                    },
                    "output_path": {
                        "type": "string",
                        "description": "Optional path to save PDF file"
                    },
                    "pdf_wait_for": {
                        "type": "number",
                        "default": 2,
                        "description": "Time to wait before generating PDF (seconds)"
                    }
                },
                required=["url"]
            )
        )

    @staticmethod
    async def execute(arguments: Dict[str, Any]) -> List[types.TextContent]:
        """Execute the PDF generation tool."""
        try:
            # Extract and validate arguments
            url = arguments.get("url")
            if not url:
                return create_error_response("URL is required", 400)

            output_path = arguments.get("output_path")
            pdf_wait_for = arguments.get("pdf_wait_for", 2)

            # Validate URL
            try:
                validated_url = validate_url(url)
            except Exception as e:
                return create_error_response(f"Invalid URL: {str(e)}", 400)

            # Generate PDF
            pdf_data = await PDFTool._generate_pdf(
                validated_url, output_path, pdf_wait_for
            )

            # Return response
            response_data = {
                "url": validated_url,
                "pdf": pdf_data,
                "success": True
            }

            if output_path:
                response_data["output_path"] = output_path

            return create_json_response(response_data)

        except Exception as e:
            return create_error_response(f"PDF generation failed: {str(e)}")

    @staticmethod
    async def _generate_pdf(
        url: str,
        output_path: Optional[str] = None,
        wait_time: float = 2
    ) -> str:
        """Generate PDF and return as base64."""

        # Configure crawler for PDF generation
        crawler_config = CrawlerRunConfig(
            pdf=True,
            pdf_wait_for=wait_time
        )

        async with AsyncWebCrawler(config=BrowserConfig()) as crawler:
            results = await crawler.arun(url=url, config=crawler_config)

            if not results or not results[0].success:
                error_msg = results[0].error_message if results else "PDF generation failed"
                raise Exception(error_msg)

            pdf_data = results[0].pdf
            if not pdf_data:
                raise Exception("No PDF data returned")

            # Save to file if output_path provided
            if output_path:
                try:
                    # Ensure directory exists
                    os.makedirs(os.path.dirname(output_path), exist_ok=True)

                    # Decode base64 and save
                    pdf_bytes = base64.b64decode(pdf_data)
                    with open(output_path, 'wb') as f:
                        f.write(pdf_bytes)
                except Exception as e:
                    raise Exception(f"Failed to save PDF to {output_path}: {str(e)}")

            return pdf_data

class JavaScriptTool:
    """Tool for executing JavaScript on web pages."""

    @staticmethod
    def get_schema() -> types.Tool:
        """Get the tool schema for MCP."""
        return types.Tool(
            name="execute_js",
            description="Execute JavaScript code on a web page and return the modified page content",
            inputSchema=create_tool_schema(
                properties={
                    "url": {
                        "type": "string",
                        "description": "Absolute http/https URL to execute JavaScript on"
                    },
                    "js_code": {
                        "oneOf": [
                            {"type": "string"},
                            {"type": "array", "items": {"type": "string"}}
                        ],
                        "description": "JavaScript code to execute (string or array of strings)"
                    }
                },
                required=["url", "js_code"]
            )
        )

    @staticmethod
    async def execute(arguments: Dict[str, Any]) -> List[types.TextContent]:
        """Execute the JavaScript tool."""
        try:
            # Extract and validate arguments
            url = arguments.get("url")
            if not url:
                return create_error_response("URL is required", 400)

            js_code = arguments.get("js_code")
            if not js_code:
                return create_error_response("JavaScript code is required", 400)

            # Validate URL
            try:
                validated_url = validate_url(url)
            except Exception as e:
                return create_error_response(f"Invalid URL: {str(e)}", 400)

            # Execute JavaScript
            result = await JavaScriptTool._execute_javascript(validated_url, js_code)

            return create_json_response(result)

        except Exception as e:
            return create_error_response(f"JavaScript execution failed: {str(e)}")

    @staticmethod
    async def _execute_javascript(
        url: str,
        js_code: Union[str, List[str]]
    ) -> Dict[str, Any]:
        """Execute JavaScript on the page and return results."""

        # Normalize js_code to list
        if isinstance(js_code, str):
            js_scripts = [js_code]
        else:
            js_scripts = js_code

        # Configure crawler for JavaScript execution
        crawler_config = CrawlerRunConfig(js_code=js_scripts)

        async with AsyncWebCrawler(config=BrowserConfig()) as crawler:
            results = await crawler.arun(url=url, config=crawler_config)

            if not results or not results[0].success:
                error_msg = results[0].error_message if results else "JavaScript execution failed"
                raise Exception(error_msg)

            # Return the full crawl result as dict
            result_dict = results[0].model_dump()

            # Add execution status
            result_dict["js_execution_success"] = True
            result_dict["executed_scripts"] = js_scripts

            return result_dict

class ContextTool:
    """Tool for querying Crawl4AI library context and documentation."""

    @staticmethod
    def get_schema() -> types.Tool:
        """Get the tool schema for MCP."""
        return types.Tool(
            name="ask",
            description="Query the Crawl4AI library context for decision making or code generation tasks. Returns extensive information about Crawl4AI functionality.",
            inputSchema=create_tool_schema(
                properties={
                    "context_type": {
                        "type": "string",
                        "enum": ["code", "doc", "all"],
                        "default": "all",
                        "description": "Specify 'code' for code context, 'doc' for documentation context, or 'all' for both"
                    },
                    "query": {
                        "type": "string",
                        "description": "RECOMMENDED search query to filter paragraphs using BM25. Leave empty to get all context."
                    },
                    "score_ratio": {
                        "type": "number",
                        "minimum": 0.0,
                        "maximum": 1.0,
                        "default": 0.5,
                        "description": "Minimum score as a fraction of the maximum score for filtering results"
                    },
                    "max_results": {
                        "type": "integer",
                        "minimum": 1,
                        "default": 20,
                        "description": "Maximum number of results to return"
                    }
                },
                required=[]
            )
        )

    @staticmethod
    async def execute(arguments: Dict[str, Any]) -> List[types.TextContent]:
        """Execute the context query tool."""
        try:
            # Extract arguments with defaults
            context_type = arguments.get("context_type", "all")
            query = arguments.get("query")
            score_ratio = arguments.get("score_ratio", 0.5)
            max_results = arguments.get("max_results", 20)

            # Get context
            result = await ContextTool._get_context(
                context_type, query, score_ratio, max_results
            )

            return create_json_response(result)

        except Exception as e:
            return create_error_response(f"Context query failed: {str(e)}")

    @staticmethod
    async def _get_context(
        context_type: str,
        query: Optional[str] = None,
        score_ratio: float = 0.5,
        max_results: int = 20
    ) -> Dict[str, Any]:
        """Get Crawl4AI library context."""

        # Load context content (placeholder implementation)
        code_content = ContextTool._load_code_context()
        doc_content = ContextTool._load_doc_context()

        # If no query, return raw contexts
        if not query:
            result = {}
            if context_type in ("code", "all"):
                result["code_context"] = code_content
            if context_type in ("doc", "all"):
                result["doc_context"] = doc_content
            return result

        # Use BM25 for filtering if available
        results = {}

        if context_type in ("code", "all"):
            code_results = ContextTool._search_content(
                code_content, query, score_ratio, max_results, "code"
            )
            results["code_results"] = code_results

        if context_type in ("doc", "all"):
            doc_results = ContextTool._search_content(
                doc_content, query, score_ratio, max_results, "doc"
            )
            results["doc_results"] = doc_results

        return results

    @staticmethod
    def _load_code_context() -> str:
        """Load code context."""
        return """# Crawl4AI Code Context

This is a basic Crawl4AI code context. For production use, replace this with actual context files.

Key components:
- AsyncWebCrawler: Main crawler class for asynchronous web crawling
- CrawlerRunConfig: Configuration for crawl operations including caching, extraction strategies
- BrowserConfig: Browser configuration for Playwright automation
- Content filtering strategies: PruningContentFilter, BM25ContentFilter, LLMContentFilter
- Markdown generation: DefaultMarkdownGenerator for converting HTML to markdown
- Extraction strategies: LLMExtractionStrategy, JsonCssExtractionStrategy for data extraction

Basic usage:
```python
async with AsyncWebCrawler() as crawler:
    result = await crawler.arun(url="https://example.com")
    print(result.markdown.fit_markdown)
```
"""

    @staticmethod
    def _load_doc_context() -> str:
        """Load documentation context."""
        return """# Crawl4AI Documentation Context

This is a basic Crawl4AI documentation context. For production use, replace this with actual context files.

Key topics:
- Installation: pip install crawl4ai, playwright install
- Basic crawling: Use AsyncWebCrawler for web scraping
- Content filtering: Choose between fit, raw, bm25, or llm filtering
- Markdown generation: Automatic conversion of HTML to clean markdown
- Browser management: Automated browser handling with Playwright
- Caching: Built-in caching for improved performance
- Configuration: Extensive configuration options for customization

Common use cases:
- Web scraping for AI training data
- Content extraction for RAG applications
- Automated documentation generation
- Website monitoring and analysis
"""

    @staticmethod
    def _search_content(
        content: str,
        query: str,
        score_ratio: float,
        max_results: int,
        content_type: str
    ) -> List[Dict[str, Any]]:
        """Search content using BM25 or simple text matching."""

        # Split content into chunks
        if content_type == "code":
            chunks = ContextTool._chunk_code_functions(content)
        else:
            chunks = ContextTool._chunk_doc_sections(content)

        if not chunks:
            return []

        # Use BM25 if available, otherwise simple text matching
        if BM25Okapi:
            return ContextTool._bm25_search(chunks, query, score_ratio, max_results)
        else:
            return ContextTool._simple_search(chunks, query, max_results)

    @staticmethod
    def _bm25_search(chunks: List[str], query: str, score_ratio: float, max_results: int) -> List[Dict[str, Any]]:
        """BM25-based search."""
        tokenized_chunks = [chunk.split() for chunk in chunks]
        bm25 = BM25Okapi(tokenized_chunks)

        query_tokens = query.split()
        scores = bm25.get_scores(query_tokens)

        if len(scores) > 0:
            max_score = float(max(scores))
            cutoff = max_score * score_ratio

            scored_chunks = []
            for i, (chunk, score) in enumerate(zip(chunks, scores)):
                if score >= cutoff:
                    scored_chunks.append({"text": chunk, "score": float(score)})

            scored_chunks.sort(key=lambda x: x["score"], reverse=True)
            return scored_chunks[:max_results]

        return []

    @staticmethod
    def _simple_search(chunks: List[str], query: str, max_results: int) -> List[Dict[str, Any]]:
        """Simple text matching search."""
        query_terms = query.lower().split()
        scored_chunks = []

        for chunk in chunks:
            chunk_lower = chunk.lower()
            score = sum(chunk_lower.count(term) for term in query_terms)
            if score > 0:
                scored_chunks.append({"text": chunk, "score": float(score)})

        scored_chunks.sort(key=lambda x: x["score"], reverse=True)
        return scored_chunks[:max_results]

    @staticmethod
    def _chunk_code_functions(content: str) -> List[str]:
        """Split code content into function/class chunks."""
        chunks = []
        lines = content.split('\n')
        current_chunk = []

        for line in lines:
            if re.match(r'^(def |class |async def )', line.strip()):
                if current_chunk:
                    chunks.append('\n'.join(current_chunk))
                current_chunk = [line]
            else:
                current_chunk.append(line)

        if current_chunk:
            chunks.append('\n'.join(current_chunk))

        return [chunk for chunk in chunks if chunk.strip()]

    @staticmethod
    def _chunk_doc_sections(content: str) -> List[str]:
        """Split documentation into sections."""
        chunks = []
        lines = content.split('\n')
        current_chunk = []

        for line in lines:
            if re.match(r'^#+\s', line.strip()):
                if current_chunk:
                    chunks.append('\n'.join(current_chunk))
                current_chunk = [line]
            else:
                current_chunk.append(line)

        if current_chunk:
            chunks.append('\n'.join(current_chunk))

        return [chunk for chunk in chunks if chunk.strip()]

# Tool registry
TOOLS = {
    "md": MarkdownTool,
    "html": HTMLTool,
    "crawl": CrawlTool,
    "screenshot": ScreenshotTool,
    "pdf": PDFTool,
    "execute_js": JavaScriptTool,
    "ask": ContextTool
}

# MCP Server handlers
@server.list_tools()
async def handle_list_tools() -> List[types.Tool]:
    """List available tools."""
    tools = []
    for tool_name, tool_class in TOOLS.items():
        try:
            tool_schema = tool_class.get_schema()
            tools.append(tool_schema)
        except Exception as e:
            logger.error(f"Error getting schema for tool {tool_name}: {e}")

    return tools

@server.call_tool()
async def handle_call_tool(
    name: str, arguments: Dict[str, Any] | None
) -> List[types.TextContent | types.ImageContent | types.EmbeddedResource]:
    """Handle tool execution requests."""

    if name not in TOOLS:
        raise ValueError(f"Unknown tool: {name}")

    tool_class = TOOLS[name]

    try:
        # Execute the tool with provided arguments
        result = await tool_class.execute(arguments or {})
        return result

    except Exception as e:
        logger.error(f"Error executing tool {name}: {e}")
        # Return error as text content
        error_response = {
            "error": 500,
            "detail": str(e),
            "success": False
        }
        return [types.TextContent(
            type="text",
            text=json.dumps(error_response)
        )]

@server.list_resources()
async def handle_list_resources() -> List[types.Resource]:
    """List available resources."""
    return []

@server.read_resource()
async def handle_read_resource(uri: str) -> str:
    """Read a specific resource."""
    raise ValueError(f"Resource not found: {uri}")

@server.list_prompts()
async def handle_list_prompts() -> List[types.Prompt]:
    """List available prompts."""
    return []

@server.get_prompt()
async def handle_get_prompt(
    name: str, arguments: Dict[str, str] | None
) -> types.GetPromptResult:
    """Get a specific prompt."""
    raise ValueError(f"Unknown prompt: {name}")

async def main():
    """Main entry point for the MCP server."""
    logger.info(f"Starting Crawl4AI MCP Server v{config.version}")
    logger.info(f"Available tools: {list(TOOLS.keys())}")

    # Run the server using stdin/stdout streams
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name=config.name,
                server_version=config.version,
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )

if __name__ == "__main__":
    print("🚀 Crawl4AI MCP Server - Standalone Edition")
    print("=" * 50)
    print(f"Server: {config.name} v{config.version}")
    print(f"Available tools: {list(TOOLS.keys())}")
    print("Waiting for MCP client connection...")
    print("=" * 50)

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        logger.error(f"Server error: {e}", exc_info=True)
