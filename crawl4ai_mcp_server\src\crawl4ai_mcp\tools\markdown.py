"""Markdown generation tool for Crawl4AI MCP Server."""

"""Markdown generation tool for Crawl4AI MCP Server."""

import os
from typing import Dict, Any, List, Optional
from enum import Enum

import mcp.types as types
from crawl4ai import Async<PERSON>ebCrawler, CrawlerRunConfig, CacheMode
from crawl4ai.content_filter_strategy import (
    PruningContentFilter, 
    BM25ContentFilter, 
    LLMContentFilter
)
from crawl4ai.markdown_generation_strategy import <PERSON>fault<PERSON><PERSON>downGenerator
from crawl4ai.web_scraping_strategy import LXMLWebScrapingStrategy
from crawl4ai import LLMConfig

from ..utils import create_json_response, create_error_response, validate_url, create_tool_schema
from ..config import config


class FilterType(str, Enum):
    """Content filter types."""
    RAW = "raw"
    FIT = "fit" 
    BM25 = "bm25"
    LLM = "llm"


class MarkdownTool:
    """Tool for generating markdown from web content."""
    
    @staticmethod
    def get_schema() -> types.Tool:
        """Get the tool schema for MCP."""
        return types.Tool(
            name="md",
            description="Generate markdown from web content with various filtering options",
            inputSchema=create_tool_schema(
                properties={
                    "url": {
                        "type": "string",
                        "description": "Absolute http/https URL to fetch"
                    },
                    "f": {
                        "type": "string", 
                        "enum": ["raw", "fit", "bm25", "llm"],
                        "default": "fit",
                        "description": "Content filter strategy: fit, raw, bm25, or llm"
                    },
                    "q": {
                        "type": "string",
                        "description": "Query string used by BM25/LLM filters"
                    },
                    "c": {
                        "type": "string",
                        "default": "0", 
                        "description": "Cache-bust / revision counter"
                    }
                },
                required=["url"]
            )
        )
    
    @staticmethod
    async def execute(arguments: Dict[str, Any]) -> List[types.TextContent]:
        """Execute the markdown generation tool."""
        try:
            # Extract and validate arguments
            url = arguments.get("url")
            if not url:
                return create_error_response("URL is required", 400)
            
            filter_type = FilterType(arguments.get("f", "fit"))
            query = arguments.get("q")
            cache = arguments.get("c", "0")
            
            # Validate URL
            try:
                validated_url = validate_url(url)
            except Exception as e:
                return create_error_response(f"Invalid URL: {str(e)}", 400)
            
            # Generate markdown
            markdown = await MarkdownTool._generate_markdown(
                validated_url, filter_type, query, cache
            )
            
            # Return response
            response_data = {
                "url": validated_url,
                "filter": filter_type.value,
                "query": query,
                "cache": cache,
                "markdown": markdown,
                "success": True
            }
            
            return create_json_response(response_data)
            
        except Exception as e:
            return create_error_response(f"Markdown generation failed: {str(e)}")
    
    @staticmethod
    async def _generate_markdown(
        url: str,
        filter_type: FilterType,
        query: Optional[str] = None,
        cache: str = "0"
    ) -> str:
        """Generate markdown from URL with specified filter."""
        
        # Configure markdown generator based on filter type
        if filter_type == FilterType.RAW:
            md_generator = DefaultMarkdownGenerator()
        else:
            content_filter = {
                FilterType.FIT: PruningContentFilter(),
                FilterType.BM25: BM25ContentFilter(user_query=query or ""),
                FilterType.LLM: LLMContentFilter(
                    llm_config=LLMConfig(
                        provider=config.llm.provider,
                        api_token=config.llm.api_token or "",
                    ),
                    instruction=query or "Extract main content"
                )
            }[filter_type]
            md_generator = DefaultMarkdownGenerator(content_filter=content_filter)
        
        # Configure cache mode
        cache_mode = CacheMode.ENABLED if cache == "1" else CacheMode.WRITE_ONLY
        
        # Crawl and generate markdown
        async with AsyncWebCrawler() as crawler:
            result = await crawler.arun(
                url=url,
                config=CrawlerRunConfig(
                    markdown_generator=md_generator,
                    scraping_strategy=LXMLWebScrapingStrategy(),
                    cache_mode=cache_mode
                )
            )
            
            if not result.success:
                raise Exception(result.error_message or "Crawl failed")
            
            return (result.markdown.raw_markdown 
                   if filter_type == FilterType.RAW 
                   else result.markdown.fit_markdown)